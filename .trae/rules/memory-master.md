---
description: Guide for memory master v2 development rules
globs: **/*
alwaysApply: true
---

- This project is hosted in docker container in ubuntu server *************.
- Never run this project locally. Only run in container.
- Always restart the container after changing to code to test.
- This project is using mem0 as a core memory library. Always check the mem0 Docs mcp server for accurate code reference and decumentation.
- All the supabase query shall be used supabase-local mcp server.