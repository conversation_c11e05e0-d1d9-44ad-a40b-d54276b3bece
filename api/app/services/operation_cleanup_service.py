"""
Operation Cleanup Service

This service handles automatic cleanup of hung operations, timeout management,
and system health monitoring for the Memory Master v2 system.
"""

import threading
import time
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import func, and_

from app.database import SessionLocal
from app.models import EvolutionOperation

logger = logging.getLogger(__name__)


class OperationCleanupService:
    """Service for managing operation timeouts and cleanup."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._cleanup_thread = None
        self._cleanup_running = False
        self._cleanup_interval = 60   # 1 minute (more frequent)
        self._operation_timeout = 1800  # 30 minutes (shorter timeout)
        self._max_hung_operations = 20  # Increased threshold for better health status
        self._cleanup_stats = {
            'total_cleanups': 0,
            'hung_operations_found': 0,
            'operations_terminated': 0,
            'last_cleanup': None
        }
        self._alerts_enabled = True
        self._alert_thresholds = {
            'hung_operations_warning': 10,   # Back to normal - issue is fixed
            'hung_operations_critical': 20,  # Back to normal - issue is fixed
            'operation_rate_warning': 200,   # operations per hour
            'operation_rate_critical': 500
        }
        self._alert_history = []

    def start_cleanup_service(self):
        """Start the background cleanup service."""
        if self._cleanup_thread is None or not self._cleanup_thread.is_alive():
            self._cleanup_running = True
            self._cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
            self._cleanup_thread.start()
            self.logger.info("Operation cleanup service started")

    def stop_cleanup_service(self):
        """Stop the background cleanup service."""
        self._cleanup_running = False
        if self._cleanup_thread and self._cleanup_thread.is_alive():
            self.logger.info("Stopping operation cleanup service...")

    def _cleanup_loop(self):
        """Main cleanup loop that runs in background."""
        self.logger.info("Operation cleanup service loop started")
        
        while self._cleanup_running:
            try:
                self._perform_cleanup()
                self._cleanup_stats['last_cleanup'] = datetime.now(timezone.utc)
                
            except Exception as e:
                self.logger.error(f"Error in cleanup loop: {e}")
            
            # Sleep for cleanup interval
            time.sleep(self._cleanup_interval)
        
        self.logger.info("Operation cleanup service loop stopped")

    def _perform_cleanup(self):
        """Perform cleanup operations."""
        try:
            db = SessionLocal()
            
            # Find hung operations (operations that have been running too long)
            cutoff_time = datetime.now(timezone.utc) - timedelta(seconds=self._operation_timeout)
            
            # Find operations older than timeout that don't have a completion status
            hung_operations = db.query(EvolutionOperation).filter(
                and_(
                    EvolutionOperation.created_at < cutoff_time,
                    # Either no metadata, no status field, or status is not completed/timed_out
                    EvolutionOperation.metadata_.op('->>')('status').is_(None)
                )
            ).all()
            
            if hung_operations:
                self.logger.warning(f"Found {len(hung_operations)} potentially hung operations")
                self._cleanup_stats['hung_operations_found'] += len(hung_operations)
                
                # Mark hung operations as timed out
                for operation in hung_operations:
                    if operation.metadata_ is None:
                        operation.metadata_ = {}
                    
                    operation.metadata_['status'] = 'timed_out'
                    operation.metadata_['cleanup_timestamp'] = datetime.now(timezone.utc).isoformat()
                    operation.metadata_['timeout_duration'] = self._operation_timeout
                    
                    self._cleanup_stats['operations_terminated'] += 1
                
                db.commit()
                self.logger.info(f"Marked {len(hung_operations)} operations as timed out")
            
            # Clean up old completed operations (older than 7 days)
            old_cutoff = datetime.now(timezone.utc) - timedelta(days=7)
            old_operations = db.query(EvolutionOperation).filter(
                and_(
                    EvolutionOperation.created_at < old_cutoff,
                    EvolutionOperation.metadata_.op('->>')('status') == 'completed'
                )
            ).count()
            
            if old_operations > 1000:  # Only clean if we have many old operations
                # Delete oldest completed operations, keeping last 1000
                db.query(EvolutionOperation).filter(
                    and_(
                        EvolutionOperation.created_at < old_cutoff,
                        EvolutionOperation.metadata_.op('->>')('status') == 'completed'
                    )
                ).limit(old_operations - 1000).delete()
                
                db.commit()
                self.logger.info(f"Cleaned up {old_operations - 1000} old completed operations")
            
            self._cleanup_stats['total_cleanups'] += 1

            # Check for alert conditions
            self._check_alert_conditions(db)

            # Check and restart worker thread if needed
            self._check_worker_thread_health()

            db.close()

        except Exception as e:
            self.logger.error(f"Error performing cleanup: {e}")
            if 'db' in locals():
                db.rollback()
                db.close()

    def _check_alert_conditions(self, db: Session):
        """Check for conditions that should trigger alerts."""
        try:
            if not self._alerts_enabled:
                return

            # Check hung operations
            cutoff_time = datetime.now(timezone.utc) - timedelta(seconds=self._operation_timeout)
            hung_count = db.query(EvolutionOperation).filter(
                and_(
                    EvolutionOperation.created_at < cutoff_time,
                    EvolutionOperation.metadata_.op('->>')('status').is_(None)
                )
            ).count()

            if hung_count >= self._alert_thresholds['hung_operations_critical']:
                self._send_alert('critical', f'Critical: {hung_count} hung operations detected')
            elif hung_count >= self._alert_thresholds['hung_operations_warning']:
                self._send_alert('warning', f'Warning: {hung_count} hung operations detected')

            # Check operation rate (last hour)
            recent_cutoff = datetime.now(timezone.utc) - timedelta(hours=1)
            recent_count = db.query(EvolutionOperation).filter(
                EvolutionOperation.created_at >= recent_cutoff
            ).count()

            if recent_count >= self._alert_thresholds['operation_rate_critical']:
                self._send_alert('critical', f'Critical: High operation rate {recent_count}/hour')
            elif recent_count >= self._alert_thresholds['operation_rate_warning']:
                self._send_alert('warning', f'Warning: High operation rate {recent_count}/hour')

        except Exception as e:
            self.logger.error(f"Error checking alert conditions: {e}")

    def _send_alert(self, level: str, message: str):
        """Send an alert (log and store in history)."""
        try:
            alert = {
                'timestamp': datetime.now(timezone.utc),
                'level': level,
                'message': message
            }

            # Log the alert
            if level == 'critical':
                self.logger.critical(f"ALERT: {message}")
            elif level == 'warning':
                self.logger.warning(f"ALERT: {message}")
            else:
                self.logger.info(f"ALERT: {message}")

            # Store in history (keep last 100 alerts)
            self._alert_history.append(alert)
            if len(self._alert_history) > 100:
                self._alert_history.pop(0)

        except Exception as e:
            self.logger.error(f"Error sending alert: {e}")

    def _check_worker_thread_health(self):
        """Check and restart worker thread if needed."""
        try:
            from app.utils.memory import MemoryClientSingleton
            memory_client = MemoryClientSingleton()

            # Get worker status
            status = memory_client.get_operation_queue_status()

            # If worker should be running but isn't alive, restart it
            if status["worker_running"] and not status["worker_alive"]:
                self.logger.warning("Worker thread should be running but isn't alive - restarting")
                memory_client._stop_worker()
                memory_client._start_worker()

                # Verify restart
                new_status = memory_client.get_operation_queue_status()
                if new_status["worker_alive"]:
                    self.logger.info("Worker thread successfully restarted")
                    self._send_alert('info', 'Worker thread automatically restarted')
                else:
                    self.logger.error("Worker thread restart failed")
                    self._send_alert('critical', 'Worker thread restart failed')

        except Exception as e:
            self.logger.error(f"Error checking worker thread health: {e}")

    def get_cleanup_stats(self) -> Dict[str, Any]:
        """Get cleanup service statistics."""
        return {
            'service_running': self._cleanup_running,
            'cleanup_interval_seconds': self._cleanup_interval,
            'operation_timeout_seconds': self._operation_timeout,
            'stats': self._cleanup_stats.copy()
        }

    def force_cleanup(self) -> Dict[str, Any]:
        """Force immediate cleanup operation."""
        try:
            self.logger.info("Forcing immediate cleanup operation")
            self._perform_cleanup()
            
            return {
                'success': True,
                'message': 'Forced cleanup completed successfully',
                'stats': self._cleanup_stats.copy()
            }
            
        except Exception as e:
            self.logger.error(f"Error in forced cleanup: {e}")
            return {
                'success': False,
                'error': str(e),
                'stats': self._cleanup_stats.copy()
            }

    def check_system_health(self) -> Dict[str, Any]:
        """Check system health related to operations."""
        try:
            db = SessionLocal()
            
            # Count operations by status
            total_operations = db.query(EvolutionOperation).count()
            
            # Count potentially hung operations
            cutoff_time = datetime.now(timezone.utc) - timedelta(seconds=self._operation_timeout)
            hung_count = db.query(EvolutionOperation).filter(
                and_(
                    EvolutionOperation.created_at < cutoff_time,
                    EvolutionOperation.metadata_.op('->>')('status').is_(None)
                )
            ).count()
            
            # Count recent operations (last hour)
            recent_cutoff = datetime.now(timezone.utc) - timedelta(hours=1)
            recent_count = db.query(EvolutionOperation).filter(
                EvolutionOperation.created_at >= recent_cutoff
            ).count()
            
            # Determine health status
            if hung_count > self._max_hung_operations:
                health_status = 'critical'
                health_message = f'Too many hung operations: {hung_count}'
            elif hung_count > 0:
                health_status = 'warning'
                health_message = f'Some hung operations detected: {hung_count}'
            else:
                health_status = 'healthy'
                health_message = 'All operations within normal parameters'
            
            db.close()
            
            return {
                'health_status': health_status,
                'health_message': health_message,
                'total_operations': total_operations,
                'hung_operations': hung_count,
                'recent_operations': recent_count,
                'cleanup_service_running': self._cleanup_running,
                'last_cleanup': self._cleanup_stats.get('last_cleanup')
            }
            
        except Exception as e:
            self.logger.error(f"Error checking system health: {e}")
            return {
                'health_status': 'error',
                'health_message': f'Health check failed: {str(e)}',
                'error': str(e)
            }

    def get_alert_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent alert history."""
        return self._alert_history[-limit:] if self._alert_history else []

    def configure_alerts(self,
                        enabled: bool = None,
                        hung_operations_warning: int = None,
                        hung_operations_critical: int = None,
                        operation_rate_warning: int = None,
                        operation_rate_critical: int = None) -> Dict[str, Any]:
        """Configure alert settings."""
        try:
            if enabled is not None:
                self._alerts_enabled = enabled

            if hung_operations_warning is not None:
                self._alert_thresholds['hung_operations_warning'] = hung_operations_warning

            if hung_operations_critical is not None:
                self._alert_thresholds['hung_operations_critical'] = hung_operations_critical

            if operation_rate_warning is not None:
                self._alert_thresholds['operation_rate_warning'] = operation_rate_warning

            if operation_rate_critical is not None:
                self._alert_thresholds['operation_rate_critical'] = operation_rate_critical

            return {
                'success': True,
                'alerts_enabled': self._alerts_enabled,
                'thresholds': self._alert_thresholds.copy()
            }

        except Exception as e:
            self.logger.error(f"Error configuring alerts: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_monitoring_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive monitoring dashboard data."""
        try:
            db = SessionLocal()

            # Get current time
            now = datetime.now(timezone.utc)

            # Operation counts by time periods
            hour_ago = now - timedelta(hours=1)
            day_ago = now - timedelta(days=1)
            week_ago = now - timedelta(weeks=1)

            ops_last_hour = db.query(EvolutionOperation).filter(
                EvolutionOperation.created_at >= hour_ago
            ).count()

            ops_last_day = db.query(EvolutionOperation).filter(
                EvolutionOperation.created_at >= day_ago
            ).count()

            ops_last_week = db.query(EvolutionOperation).filter(
                EvolutionOperation.created_at >= week_ago
            ).count()

            # Operation type distribution
            add_ops = db.query(EvolutionOperation).filter(
                EvolutionOperation.operation_type == 'ADD'
            ).count()

            update_ops = db.query(EvolutionOperation).filter(
                EvolutionOperation.operation_type == 'UPDATE'
            ).count()

            delete_ops = db.query(EvolutionOperation).filter(
                EvolutionOperation.operation_type == 'DELETE'
            ).count()

            noop_ops = db.query(EvolutionOperation).filter(
                EvolutionOperation.operation_type == 'NOOP'
            ).count()

            # System health
            health = self.check_system_health()

            db.close()

            return {
                'timestamp': now.isoformat(),
                'operation_counts': {
                    'last_hour': ops_last_hour,
                    'last_day': ops_last_day,
                    'last_week': ops_last_week
                },
                'operation_types': {
                    'ADD': add_ops,
                    'UPDATE': update_ops,
                    'DELETE': delete_ops,
                    'NOOP': noop_ops
                },
                'system_health': health,
                'cleanup_stats': self._cleanup_stats.copy(),
                'alert_config': {
                    'enabled': self._alerts_enabled,
                    'thresholds': self._alert_thresholds.copy()
                },
                'recent_alerts': self.get_alert_history(10)
            }

        except Exception as e:
            self.logger.error(f"Error getting monitoring dashboard: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }


# Global service instance
cleanup_service = OperationCleanupService()
